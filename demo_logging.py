#!/usr/bin/env python3
"""
Comprehensive logging demonstration
"""

import os
import time
import requests

# Set environment variables for detailed logging
os.environ['DEBUG'] = 'True'
os.environ['LOG_LEVEL'] = 'DEBUG'
os.environ['LOG_FORMAT'] = 'detailed'

print("🚀 Starting comprehensive logging demonstration...")
print("=" * 60)

# Import after setting environment variables
from app import app
from logging_config import get_logger

logger = get_logger(__name__)

def demonstrate_logging_levels():
    """Demonstrate different logging levels"""
    print("\n📊 Testing different log levels:")
    print("-" * 40)
    
    logger.debug("🔍 DEBUG: This is a debug message")
    logger.info("ℹ️  INFO: This is an info message")
    logger.warning("⚠️  WARNING: This is a warning message")
    logger.error("❌ ERROR: This is an error message")
    
    # Test with extra context
    logger.info("📝 INFO with context", extra={
        'user_id': 12345,
        'action': 'demo_logging',
        'timestamp': time.time()
    })

def demonstrate_request_logging():
    """Demonstrate request logging with Flask test client"""
    print("\n🌐 Testing request logging:")
    print("-" * 40)
    
    with app.test_client() as client:
        # Test health endpoint
        print("Testing /health endpoint...")
        response = client.get('/health')
        print(f"✅ Health check: {response.status_code}")
        
        # Test main page (will likely fail but show error logging)
        print("Testing / endpoint...")
        response = client.get('/', headers={'Host': 'demo.example.com'})
        print(f"📄 Main page: {response.status_code}")
        
        # Test other endpoints
        endpoints = ['/greeting', '/rsvp', '/angpao']
        for endpoint in endpoints:
            print(f"Testing {endpoint} endpoint...")
            response = client.get(endpoint, headers={'Host': 'demo.example.com'})
            print(f"📋 {endpoint}: {response.status_code}")

def demonstrate_external_request_logging():
    """Demonstrate external request logging"""
    print("\n🌍 Testing external request logging:")
    print("-" * 40)
    
    from utils import fetch_content, fetch_content_with_headers
    
    # Test HEAD request
    print("Testing HEAD request...")
    result = fetch_content_with_headers("https://httpbin.org/status/200")
    print(f"✅ HEAD request success: {result['success']}")
    
    # Test GET request
    print("Testing GET request...")
    result = fetch_content("https://httpbin.org/json")
    print(f"✅ GET request success: {result['success']}")
    
    # Test failed request (to show error logging)
    print("Testing failed request...")
    result = fetch_content("https://httpbin.org/status/500")
    print(f"❌ Failed request handled: {not result['success']}")

def show_log_file_contents():
    """Show contents of log file if it exists"""
    print("\n📁 Log file contents:")
    print("-" * 40)
    
    log_file = 'logs/app.log'
    if os.path.exists(log_file):
        print(f"📄 Reading from {log_file}:")
        with open(log_file, 'r') as f:
            lines = f.readlines()
            # Show last 15 lines
            for line in lines[-15:]:
                print(f"  {line.strip()}")
    else:
        print(f"❌ Log file {log_file} not found")

if __name__ == '__main__':
    try:
        # Run all demonstrations
        demonstrate_logging_levels()
        demonstrate_request_logging()
        demonstrate_external_request_logging()
        show_log_file_contents()
        
        print("\n" + "=" * 60)
        print("✅ Logging demonstration completed successfully!")
        print("📝 All logs above appeared in the CLI console")
        print("📁 Logs are also saved to logs/app.log file")
        print("🔍 Each request has a unique ID for tracing")
        
    except Exception as e:
        logger.error("❌ Error during demonstration", extra={
            'error': str(e),
            'error_type': type(e).__name__
        }, exc_info=True)
        print(f"❌ Error: {e}")
