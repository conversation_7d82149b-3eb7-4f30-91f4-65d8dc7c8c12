#!/usr/bin/env python3
"""
Migration script to help transition from PHP to Python.
This script will backup PHP files and clean up unnecessary files.
"""

import os
import shutil
from datetime import datetime

def backup_php_files():
    """Backup PHP files to a backup directory"""
    backup_dir = f"php_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    php_files = [
        'index.php',
        'greeting.php', 
        'rsvp.php',
        'guestbook.php',
        'angpao.php',
        'livestream.php',
        'composer.json',
        'composer.lock'
    ]
    
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
        print(f"Created backup directory: {backup_dir}")
    
    for file in php_files:
        if os.path.exists(file):
            shutil.copy2(file, backup_dir)
            print(f"Backed up: {file}")
    
    # Also backup vendor directory if it exists
    if os.path.exists('vendor'):
        shutil.copytree('vendor', os.path.join(backup_dir, 'vendor'))
        print("Backed up: vendor directory")
    
    print(f"\nBackup completed in: {backup_dir}")
    return backup_dir

def remove_php_files():
    """Remove PHP files after backup"""
    php_files = [
        'index.php',
        'greeting.php',
        'rsvp.php', 
        'guestbook.php',
        'angpao.php',
        'livestream.php',
        'composer.json',
        'composer.lock'
    ]
    
    for file in php_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"Removed: {file}")
    
    # Remove vendor directory
    if os.path.exists('vendor'):
        shutil.rmtree('vendor')
        print("Removed: vendor directory")

def main():
    print("PHP to Python Migration Script")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not os.path.exists('nixpacks.toml'):
        print("Error: nixpacks.toml not found. Are you in the right directory?")
        return
    
    # Ask for confirmation
    response = input("\nThis will backup and remove PHP files. Continue? (y/N): ")
    if response.lower() != 'y':
        print("Migration cancelled.")
        return
    
    # Backup PHP files
    print("\n1. Backing up PHP files...")
    backup_dir = backup_php_files()
    
    # Remove PHP files
    print("\n2. Removing PHP files...")
    remove_php_files()
    
    print("\n3. Migration completed!")
    print(f"   - PHP files backed up to: {backup_dir}")
    print("   - Python application is ready to use")
    print("\nNext steps:")
    print("1. Copy .env.example to .env and configure")
    print("2. Install Python dependencies: pip install -r requirements.txt")
    print("3. Run the application: python app.py")

if __name__ == '__main__':
    main()
