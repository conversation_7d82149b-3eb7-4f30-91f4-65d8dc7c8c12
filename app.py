from flask import Flask, request, make_response, render_template_string
from config import Config
from utils import (
    get_hostname, build_query_params, fetch_content_with_headers,
    fetch_content, extract_meta_data, parse_url_full_custom,
    load_fallback_html, process_html_content
)
from logging_config import setup_logging, get_logger
import os

app = Flask(__name__)
app.config.from_object(Config)

# Setup logging
setup_logging(app)
logger = get_logger(__name__)

def render_full_custom(url, headers):
    """Render full custom page with metadata"""
    no_index = headers.get('x-no-index', '0')
    custom_title = headers.get('x-custom-title', '')
    event_date = headers.get('x-event-date')
    wedding_city = headers.get('x-event-city', '')
    bride_name = headers.get('x-bride-name', '')
    groom_name = headers.get('x-groom-name', '')
    switch_name = headers.get('x-change-name', '0') == '1'
    wedding_url = headers.get('x-wedding-url', 'https://viding.co')
    wedding_domain = headers.get('x-wedding-domain', 'viding.co')
    thumbnail_url = headers.get('x-thumbnail-url', 'https://viding.co/images/icon/VIDING-WHITE-BG-PINK.jpg')

    # Build meta title
    if custom_title:
        meta_title = custom_title.strip()
    else:
        first_name = bride_name if switch_name else groom_name
        second_name = groom_name if switch_name else bride_name
        date_str = ''
        if event_date:
            try:
                from datetime import datetime
                date_obj = datetime.strptime(event_date, '%Y-%m-%d')
                date_str = date_obj.strftime('%d %B %Y')
            except:
                date_str = event_date

        meta_title = f"Welcome to the Wedding of {first_name.title()} & {second_name.title()} {wedding_city.title()} {date_str}".strip()

    # Build meta description
    first_name = bride_name if switch_name else groom_name
    second_name = groom_name if switch_name else bride_name
    meta_description = f"{first_name.title()} & {second_name.title()} Digital Invitation by Viding.co"

    template = '''
<!DOCTYPE html>
<html lang="en" style="overscroll-behavior: none;">
<head>
    <!-- Required meta tags -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="keywords" content="Virtual Wedding, Digital Invitation, Wedding Invitation, Marvelous Wedding, Unforgettable Wedding, Viding.co">
    {% if no_index == '1' %}
    <meta name="robots" content="noindex">
    {% endif %}
    <meta name="title" content="{{ meta_title }}">
    <meta name="description" content="{{ meta_description }}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="{{ wedding_domain }}" />
    <meta property="og:url" content="{{ wedding_url }}" />
    <meta property="og:title" content="{{ meta_title }}">
    <meta property="og:description" content="Digital Invitation by Viding.co">
    <meta property="og:image" content="{{ thumbnail_url }}">
    <meta property="og:locale" content="id_ID">
    <meta property="og:image:type" content="image/jpeg">
    <meta property="og:image:width" content="650">
    <meta property="og:image:height" content="366">
    <meta property="og:image:alt" content="{{ meta_title }}">

    <!-- Twitter Card Data -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@vidingco">
    <meta name="twitter:title" content="{{ meta_title }}">
    <meta name="twitter:description" content="Digital Invitation by Viding.co">
    <meta name="twitter:image:src" content="{{ thumbnail_url }}">

    <title>{{ meta_title }}</title>

    <!-- Favicons -->
    <link rel="icon" href="{{ thumbnail_url }}" sizes="192x192" />
    <link rel="icon" href="{{ thumbnail_url }}" sizes="32x32" />
    <link rel="icon" href="{{ thumbnail_url }}" sizes="16x16" />

    <!-- Google Translate -->
    <meta name="google" content="notranslate">
    <link rel="preconnect" href="https://media.viding.co">

    <!-- JSON-LD Schema Markup -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "headline": "{{ meta_title }}",
        "url": "{{ wedding_url }}",
        "image": "{{ thumbnail_url }}",
        "thumbnailUrl": "{{ thumbnail_url }}"
    }
    </script>
    <style type="text/css">
        html {
            overflow: auto;
        }

        html,
        body,
        div,
        #fullFrame {
            margin: 0px;
            padding: 0px;
            height: 100%;
            border: none;
        }

        #fullFrame {
            display: block;
            width: 100%;
            border: none;
            overflow-y: auto;
            overflow-x: hidden;
        }
        body {
            overflow: hidden;
        }
    </style>
</head>

<body>
    <iframe id="fullFrame" src="{{ url }}"
        frameborder="0" marginheight="0" marginwidth="0" width="100%" height="100%" scrolling="auto" allowfullscreen>
    </iframe>
    <script>
        if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
            document.querySelector('meta[name=viewport]')
                .setAttribute(
                    'content',
                    'initial-scale=1.0001, minimum-scale=1.0001, maximum-scale=1.0001, user-scalable=no'
                );
        }
        // Enforce no zoom on other mobile devices
        document.addEventListener("gesturestart", function (e) {
            e.preventDefault(); // Prevent pinch zoom
        });

        document.addEventListener("dblclick", function (e) {
            e.preventDefault(); // Prevent double-tap zoom
        });
    </script>
</body>
</html>
    '''

    return render_template_string(template,
                                url=url,
                                no_index=no_index,
                                meta_title=meta_title,
                                meta_description=meta_description,
                                wedding_domain=wedding_domain,
                                wedding_url=wedding_url,
                                thumbnail_url=thumbnail_url)

@app.route('/')
def index():
    """Main index route - equivalent to index.php"""
    viding_host = Config.VIDING_HOST
    hostname = get_hostname()
    query_params = build_query_params()

    logger.info("Processing index request", extra={
        'hostname': hostname,
        'viding_host': viding_host,
        'query_params': query_params
    })

    # Build the format URL
    format_url = f"https://{viding_host}/tamuinvit/{hostname}"
    if query_params:
        format_url += f"?{query_params}"

    logger.debug("Built format URL", extra={'format_url': format_url})

    # Check the URL with HEAD request first
    response_info = fetch_content_with_headers(format_url)

    if response_info['status_code'] != 200:
        logger.warning("Format URL not available, trying coming soon page", extra={
            'format_url': format_url,
            'status_code': response_info['status_code']
        })

        # Return coming soon page or fallback
        coming_soon_url = f"https://{viding_host}/comingsoon"
        content_response = fetch_content(coming_soon_url)

        if content_response['success']:
            logger.info("Serving coming soon page")
            return content_response['content']
        else:
            logger.warning("Coming soon page failed, serving fallback HTML")
            return load_fallback_html()

    # Check for custom headers
    headers = response_info['headers']
    custom_header_detected = headers.get('x-custom-builder') == '1'
    new_url = headers.get('x-builder-theme-url', '').strip()

    logger.debug("Checking custom headers", extra={
        'custom_header_detected': custom_header_detected,
        'new_url': new_url,
        'headers_count': len(headers)
    })

    if custom_header_detected and new_url:
        logger.info("Custom builder detected, rendering full custom page", extra={
            'new_url': new_url
        })
        # Handle custom builder
        link = parse_url_full_custom(new_url, query_params)
        return render_full_custom(link, headers)

    # Fetch the actual content
    content_response = fetch_content(format_url)
    if not content_response['success']:
        logger.error("Failed to fetch content, serving fallback", extra={
            'format_url': format_url,
            'error': content_response.get('error', 'Unknown error')
        })
        return load_fallback_html()

    # Set cookie and process HTML
    current_url = request.url
    html_content = content_response['content']
    processed_html = process_html_content(html_content, hostname, viding_host)

    logger.info("Successfully processed content", extra={
        'content_length': len(html_content),
        'processed_length': len(processed_html),
        'current_url': current_url
    })

    # Create response with cookie
    response = make_response(processed_html)
    response.set_cookie('invitation_url', current_url, domain=viding_host, path='/')

    return response

def create_simple_page_template():
    """Template for simple pages like greeting, rsvp, etc."""
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="UTF-8">
    <title>{{ title }}</title>
    <link rel="apple-touch-icon" href="{{ thumbnail }}" />
    <style type="text/css">
        html, body, div {
            margin: 0;
            padding: 0;
            border: none;
        }
    </style>
</head>
<body>
    <div id="content">
        {{ content | safe }}
    </div>
    <script>
        // Adjust viewport for iOS devices
        if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
            document.querySelector('meta[name=viewport]')
                .setAttribute(
                    'content',
                    'initial-scale=1.0001, minimum-scale=1.0001, maximum-scale=1.0001, user-scalable=no'
                );
        }
    </script>
</body>
</html>
    '''

def create_iframe_template():
    """Template for iframe pages like guestbook"""
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Required meta tags -->
  <meta charset="UTF-8">
  {{ title_element | safe }}
  <style type="text/css">
  html { overflow: auto; }

  html, body, div, #fullFrame {
    margin: 0px;
    padding: 0px;
    height: 100%;
    border: none;
  }

  #fullFrame {
    display: block;
    width: 100%;
    border: none;
    overflow-y: auto;
    overflow-x: hidden;
  }
  </style>
</head>

<body>
  <iframe id="fullFrame" src="{{ url }}"
    frameborder="0" marginheight="0" marginwidth="0" width="100%" height="100%" scrolling="auto" allowfullscreen>
  </iframe>
  <script>
  if(/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream){
    document.querySelector('meta[name=viewport]')
    .setAttribute(
      'content',
      'initial-scale=1.0001, minimum-scale=1.0001, maximum-scale=1.0001, user-scalable=no'
    );
  }
  </script>
</body>
</html>
    '''

def handle_simple_page(endpoint):
    """Handle simple pages (greeting, rsvp, angpao, livestream)"""
    viding_host = Config.VIDING_HOST
    hostname = get_hostname()
    format_url = f"https://{viding_host}/tamuinvit/{hostname}/{endpoint}"

    logger.info("Processing simple page request", extra={
        'endpoint': endpoint,
        'hostname': hostname,
        'format_url': format_url
    })

    # Fetch content
    content_response = fetch_content(format_url)
    if not content_response['success']:
        logger.error("Failed to fetch simple page content", extra={
            'endpoint': endpoint,
            'format_url': format_url,
            'error': content_response.get('error', 'Unknown error')
        })
        return load_fallback_html()

    # Extract metadata
    meta_data = extract_meta_data(content_response['content'], viding_host)

    logger.debug("Extracted metadata for simple page", extra={
        'endpoint': endpoint,
        'title': meta_data['title'],
        'thumbnail': meta_data['thumbnail']
    })

    # Render template
    template = create_simple_page_template()
    return render_template_string(template,
                                title=meta_data['title'],
                                thumbnail=meta_data['thumbnail'],
                                content=content_response['content'])

@app.route('/greeting')
def greeting():
    """Greeting page route - equivalent to greeting.php"""
    return handle_simple_page('greeting')

@app.route('/rsvp')
def rsvp():
    """RSVP page route - equivalent to rsvp.php"""
    return handle_simple_page('rsvp')

@app.route('/angpao')
def angpao():
    """Angpao page route - equivalent to angpao.php"""
    return handle_simple_page('angpao')

@app.route('/livestream')
def livestream():
    """Livestream page route - equivalent to livestream.php"""
    return handle_simple_page('livestream')

@app.route('/guestbook')
def guestbook():
    """Guestbook page route - equivalent to guestbook.php"""
    viding_host = Config.VIDING_HOST
    hostname = get_hostname()
    format_url = f"https://{viding_host}/tamuinvit/{hostname}/guestbook"

    logger.info("Processing guestbook request", extra={
        'hostname': hostname,
        'format_url': format_url
    })

    # Fetch content
    content_response = fetch_content(format_url)
    if not content_response['success']:
        logger.error("Failed to fetch guestbook content", extra={
            'format_url': format_url,
            'error': content_response.get('error', 'Unknown error')
        })
        return load_fallback_html()

    # Extract title and thumbnail using regex (similar to PHP version)
    import re
    pattern = r'<title>(.*?)</title>.*?<link.*?rel="apple-touch-icon".*?href="(.*?)"/?>'
    match = re.search(pattern, content_response['content'], re.DOTALL)

    if match:
        title = match.group(1)
        thumbnail = f"https://{viding_host}{match.group(2)}"
        title_element = match.group(0)  # The entire matched element
        logger.debug("Extracted guestbook metadata from content", extra={
            'title': title,
            'thumbnail': thumbnail
        })
    else:
        title = 'Default Title'
        thumbnail = Config.DEFAULT_THUMBNAIL
        title_element = f'<title>{title}</title><link rel="apple-touch-icon" href="{thumbnail}" />'
        logger.warning("Could not extract guestbook metadata, using defaults", extra={
            'title': title,
            'thumbnail': thumbnail
        })

    # Render iframe template
    template = create_iframe_template()
    return render_template_string(template,
                                url=format_url,
                                title_element=title_element)

@app.route('/health')
def health():
    """Health check endpoint"""
    logger.debug("Health check requested")
    return {
        'status': 'healthy',
        'service': 'viding-static',
        'version': '1.0.0'
    }

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 80))
    logger.info("Starting Flask application", extra={
        'debug': Config.DEBUG,
        'host': '0.0.0.0',
        'port': port
    })
    app.run(debug=Config.DEBUG, host='0.0.0.0', port=port)
