version: '3.8'

services:
  web:
    build: .
    ports:
      - "80:80"
    environment:
      - HOST=${HOST:-viding.co}
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key-change-in-production}
      - DEBUG=${DEBUG:-False}
      - PORT=80
    volumes:
      # Mount source code for development (comment out for production)
      - .:/app
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:80/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
  # Optional: Add nginx reverse proxy for production
  # nginx:
  #   image: nginx:alpine
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx.conf:/etc/nginx/nginx.conf:ro
  #   depends_on:
  #     - web
  #   restart: unless-stopped
