# viding-static

A Python Flask web application that serves as a proxy/wrapper for wedding invitation content from Viding.co.

## Overview

This application was converted from PHP to Python and provides the following functionality:

- Main invitation page proxy
- Greeting page
- RSVP page
- Guestbook page
- <PERSON><PERSON><PERSON> page
- Livestream page

## Requirements

- Python 3.12+
- Flask
- python-dotenv
- requests
- gunicorn (for production)

## Installation

1. Clone the repository
2. Install dependencies:

   ```bash
   pip install -r requirements.txt
   ```

3. Copy the environment file:

   ```bash
   cp .env.example .env
   ```

4. Edit `.env` file with your configuration

## Development

### Local Development

Run the development server:

```bash
python app.py
```

The application will be available at `http://localhost:80`

### Docker Development

1. Build the Docker image:

   ```bash
   ./docker-run.sh build
   ```

2. Run in development mode:

   ```bash
   ./docker-run.sh dev
   ```

3. Or use docker-compose:

   ```bash
   docker-compose up --build
   ```

## Production Deployment

### Using Nixpacks (Railway, etc.)

The application is configured for deployment using nixpacks with:

- <PERSON>in<PERSON> as reverse proxy
- Gunicorn as WSGI server
- Supervisor for process management

### Using Docker

1. Build production image:

   ```bash
   ./docker-run.sh build-prod
   ```

2. Run production container:

   ```bash
   docker run -d \
     --name viding-static-prod \
     -p 80:80 \
     --env-file .env \
     viding-static:latest-prod
   ```

3. Or use the helper script:

   ```bash
   ./docker-run.sh run -p 80
   ```

### Docker Commands

The `docker-run.sh` script provides convenient commands:

- `./docker-run.sh build` - Build development image
- `./docker-run.sh build-prod` - Build production image
- `./docker-run.sh run` - Run container
- `./docker-run.sh dev` - Run in development mode
- `./docker-run.sh stop` - Stop and remove containers
- `./docker-run.sh logs` - Show container logs
- `./docker-run.sh shell` - Open shell in container
- `./docker-run.sh clean` - Remove images and containers

## Environment Variables

- `HOST`: The Viding host to proxy requests to (default: viding.co)
- `SECRET_KEY`: Flask secret key for sessions
- `DEBUG`: Enable debug mode (default: False)
- `PORT`: Port to run the application on (default: 80)

## Routes

- `/` - Main invitation page
- `/greeting` - Greeting page
- `/rsvp` - RSVP page
- `/guestbook` - Guestbook page
- `/angpao` - Angpao page
- `/livestream` - Livestream page
