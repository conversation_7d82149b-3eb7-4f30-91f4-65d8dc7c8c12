# viding-static

A Python Flask web application that serves as a proxy/wrapper for wedding invitation content from Viding.co.

## Overview

This application was converted from PHP to Python and provides the following functionality:

- Main invitation page proxy
- Greeting page
- RSVP page
- Guestbook page
- <PERSON><PERSON><PERSON> page
- Livestream page

## Requirements

- Python 3.12+
- Flask
- python-dotenv
- requests
- gunicorn (for production)

## Installation

1. Clone the repository
2. Install dependencies:

   ```bash
   pip install -r requirements.txt
   ```

3. Copy the environment file:

   ```bash
   cp .env.example .env
   ```

4. Edit `.env` file with your configuration

## Development

### Local Development

Run the development server:

```bash
python app.py
```

The application will be available at `http://localhost:80`

### Docker Development

1. Build the Docker image:

   ```bash
   ./docker-run.sh build
   ```

2. Run in development mode:

   ```bash
   ./docker-run.sh dev
   ```

3. Or use docker-compose:

   ```bash
   docker-compose up --build
   ```

## Production Deployment

### Using Nixpacks (Railway, etc.)

The application is configured for deployment using nixpacks with:

- <PERSON>in<PERSON> as reverse proxy
- Gunicorn as WSGI server
- Supervisor for process management

### Using Docker

1. Build production image:

   ```bash
   ./docker-run.sh build-prod
   ```

2. Run production container:

   ```bash
   docker run -d \
     --name viding-static-prod \
     -p 80:80 \
     --env-file .env \
     viding-static:latest-prod
   ```

3. Or use the helper script:

   ```bash
   ./docker-run.sh run -p 80
   ```

### Docker Commands

The `docker-run.sh` script provides convenient commands:

- `./docker-run.sh build` - Build development image
- `./docker-run.sh build-prod` - Build production image
- `./docker-run.sh run` - Run container
- `./docker-run.sh dev` - Run in development mode
- `./docker-run.sh stop` - Stop and remove containers
- `./docker-run.sh logs` - Show container logs
- `./docker-run.sh shell` - Open shell in container
- `./docker-run.sh clean` - Remove images and containers

## Environment Variables

### Core Configuration

- `HOST`: The Viding host to proxy requests to (default: viding.co)
- `SECRET_KEY`: Flask secret key for sessions
- `DEBUG`: Enable debug mode (default: False)
- `PORT`: Port to run the application on (default: 80)

### Logging Configuration

- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL) (default: INFO in production, DEBUG in development)
- `LOG_FILE`: Path to log file (default: logs/app.log)
- `LOG_FORMAT`: Log format - 'json' for production, 'detailed' for development (default: json in production, detailed in development)
- `LOG_MAX_BYTES`: Maximum log file size in bytes before rotation (default: 10485760 = 10MB)
- `LOG_BACKUP_COUNT`: Number of backup log files to keep (default: 5)

### Request Configuration

- `REQUEST_TIMEOUT`: Timeout for external HTTP requests in seconds (default: 30)
- `SLOW_REQUEST_THRESHOLD`: Threshold in seconds to log slow requests (default: 2.0)

## Logging

The application includes comprehensive logging with the following features:

### Log Levels

- **DEBUG**: Detailed information for debugging
- **INFO**: General information about application flow
- **WARNING**: Warning messages for potential issues
- **ERROR**: Error messages for failed operations
- **CRITICAL**: Critical errors that may cause application failure

### Log Formats

- **Development**: Human-readable format with detailed context
- **Production**: JSON format for structured logging and log aggregation

### What Gets Logged

- **Request/Response**: All incoming requests and their responses with timing
- **External API Calls**: All HTTP requests to Viding.co with performance metrics
- **Errors**: All exceptions and error conditions with full context
- **Performance**: Slow requests that exceed the configured threshold
- **Application Events**: Route processing, fallback scenarios, metadata extraction

### Log Files

- **Application Logs**: `logs/app.log` (with rotation)
- **Gunicorn Access Logs**: `logs/access.log` (in production)
- **Gunicorn Error Logs**: `logs/error.log` (in production)

### Request Tracing

Each request gets a unique request ID for tracing across log entries:

```text
2024-01-15 10:30:45 - INFO - [abc12345] - Request started
2024-01-15 10:30:45 - INFO - [abc12345] - Starting GET request
2024-01-15 10:30:46 - INFO - [abc12345] - Request completed
```

### Viewing Logs

#### Development

```bash
# View application logs
tail -f logs/app.log

# View logs with JSON formatting
tail -f logs/app.log | jq .
```

#### Production (Docker)

```bash
# View container logs
docker logs viding-static-prod

# View application logs
docker exec viding-static-prod tail -f logs/app.log

# View access logs
docker exec viding-static-prod tail -f logs/access.log
```

## Routes

- `/` - Main invitation page
- `/greeting` - Greeting page
- `/rsvp` - RSVP page
- `/guestbook` - Guestbook page
- `/angpao` - Angpao page
- `/livestream` - Livestream page
