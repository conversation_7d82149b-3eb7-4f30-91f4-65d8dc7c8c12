#!/usr/bin/env python3
"""
Test script to verify logging functionality
"""

import os
import sys
import time
import requests
from datetime import datetime

def test_logging():
    """Test the logging functionality by making requests to the application"""
    
    # Set environment variables for testing
    os.environ['DEBUG'] = 'True'
    os.environ['LOG_LEVEL'] = 'DEBUG'
    os.environ['LOG_FORMAT'] = 'detailed'
    
    print("Testing logging functionality...")
    print("=" * 50)
    
    # Import after setting environment variables
    from app import app
    from logging_config import get_logger
    
    logger = get_logger(__name__)
    
    # Test different log levels
    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    
    # Test with extra context
    logger.info("Testing with extra context", extra={
        'user_id': 12345,
        'action': 'test_logging',
        'timestamp': datetime.utcnow().isoformat()
    })
    
    print("\nStarting Flask test client...")
    
    with app.test_client() as client:
        # Test health endpoint
        print("Testing /health endpoint...")
        response = client.get('/health')
        print(f"Health check status: {response.status_code}")
        
        # Test main index (this will likely fail due to external dependencies)
        print("Testing / endpoint...")
        response = client.get('/', headers={'Host': 'test.example.com'})
        print(f"Index status: {response.status_code}")
        
        # Test other endpoints
        endpoints = ['/greeting', '/rsvp', '/angpao', '/livestream', '/guestbook']
        for endpoint in endpoints:
            print(f"Testing {endpoint} endpoint...")
            response = client.get(endpoint, headers={'Host': 'test.example.com'})
            print(f"{endpoint} status: {response.status_code}")
    
    print("\nLogging test completed!")
    print("Check logs/app.log for detailed log output")
    
    # Show recent log entries if log file exists
    log_file = 'logs/app.log'
    if os.path.exists(log_file):
        print(f"\nRecent log entries from {log_file}:")
        print("-" * 50)
        with open(log_file, 'r') as f:
            lines = f.readlines()
            # Show last 10 lines
            for line in lines[-10:]:
                print(line.strip())
    else:
        print(f"\nLog file {log_file} not found")

if __name__ == '__main__':
    test_logging()
