#!/bin/bash

# Docker helper script for viding-static Python app

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="viding-static"
TAG="latest"
PORT="80"

# Functions
print_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build       Build the Docker image"
    echo "  build-prod  Build production Docker image"
    echo "  run         Run the container"
    echo "  dev         Run in development mode with volume mount"
    echo "  stop        Stop and remove the container"
    echo "  logs        Show container logs"
    echo "  shell       Open shell in running container"
    echo "  clean       Remove image and containers"
    echo ""
    echo "Options:"
    echo "  -p, --port PORT     Set port (default: 80)"
    echo "  -t, --tag TAG       Set image tag (default: latest)"
    echo "  -h, --help          Show this help"
}

build_image() {
    echo -e "${GREEN}Building Docker image: ${IMAGE_NAME}:${TAG}${NC}"
    docker build -t "${IMAGE_NAME}:${TAG}" .
    echo -e "${GREEN}Build completed!${NC}"
}

build_prod_image() {
    echo -e "${GREEN}Building production Docker image: ${IMAGE_NAME}:${TAG}-prod${NC}"
    docker build -f Dockerfile.prod -t "${IMAGE_NAME}:${TAG}-prod" .
    echo -e "${GREEN}Production build completed!${NC}"
}

run_container() {
    echo -e "${GREEN}Running container on port ${PORT}${NC}"
    docker run -d \
        --name "${IMAGE_NAME}" \
        -p "${PORT}:80" \
        --env-file .env \
        "${IMAGE_NAME}:${TAG}"
    echo -e "${GREEN}Container started! Access at http://localhost:${PORT}${NC}"
}

run_dev() {
    echo -e "${GREEN}Running in development mode with volume mount${NC}"
    docker run -d \
        --name "${IMAGE_NAME}-dev" \
        -p "${PORT}:80" \
        -v "$(pwd):/app" \
        --env-file .env \
        -e DEBUG=True \
        "${IMAGE_NAME}:${TAG}"
    echo -e "${GREEN}Development container started! Access at http://localhost:${PORT}${NC}"
}

stop_container() {
    echo -e "${YELLOW}Stopping and removing containers${NC}"
    docker stop "${IMAGE_NAME}" 2>/dev/null || true
    docker rm "${IMAGE_NAME}" 2>/dev/null || true
    docker stop "${IMAGE_NAME}-dev" 2>/dev/null || true
    docker rm "${IMAGE_NAME}-dev" 2>/dev/null || true
    echo -e "${GREEN}Containers stopped and removed${NC}"
}

show_logs() {
    echo -e "${GREEN}Showing container logs${NC}"
    if docker ps | grep -q "${IMAGE_NAME}"; then
        docker logs -f "${IMAGE_NAME}"
    elif docker ps | grep -q "${IMAGE_NAME}-dev"; then
        docker logs -f "${IMAGE_NAME}-dev"
    else
        echo -e "${RED}No running container found${NC}"
        exit 1
    fi
}

open_shell() {
    echo -e "${GREEN}Opening shell in container${NC}"
    if docker ps | grep -q "${IMAGE_NAME}"; then
        docker exec -it "${IMAGE_NAME}" /bin/bash
    elif docker ps | grep -q "${IMAGE_NAME}-dev"; then
        docker exec -it "${IMAGE_NAME}-dev" /bin/bash
    else
        echo -e "${RED}No running container found${NC}"
        exit 1
    fi
}

clean_all() {
    echo -e "${YELLOW}Cleaning up Docker images and containers${NC}"
    stop_container
    docker rmi "${IMAGE_NAME}:${TAG}" 2>/dev/null || true
    docker rmi "${IMAGE_NAME}:${TAG}-prod" 2>/dev/null || true
    echo -e "${GREEN}Cleanup completed${NC}"
}

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
    build)
        build_image
        exit 0
        ;;
    build-prod)
        build_prod_image
        exit 0
        ;;
    run)
        run_container
        exit 0
        ;;
    dev)
        run_dev
        exit 0
        ;;
    stop)
        stop_container
        exit 0
        ;;
    logs)
        show_logs
        exit 0
        ;;
    shell)
        open_shell
        exit 0
        ;;
    clean)
        clean_all
        exit 0
        ;;
    -p | --port)
        PORT="$2"
        shift 2
        ;;
    -t | --tag)
        TAG="$2"
        shift 2
        ;;
    -h | --help)
        print_usage
        exit 0
        ;;
    *)
        echo -e "${RED}Unknown option: $1${NC}"
        print_usage
        exit 1
        ;;
    esac
done

# If no command provided, show usage
print_usage
