import re
import requests
from urllib.parse import urlencode, parse_qs, urlparse
from flask import request
from config import Config

def get_hostname():
    """Extract hostname from request, removing www prefix if present"""
    host = request.headers.get('Host', '')
    if host.startswith('www.'):
        return host[4:]
    return host

def build_query_params():
    """Build query parameters from request args"""
    params = {}

    if request.args.get('name'):
        params['name'] = request.args.get('name')
    if request.args.get('event'):
        params['event'] = request.args.get('event')
    if request.args.get('rsvp') is not None:
        params['rsvp'] = request.args.get('rsvp')

    return urlencode(params)

def fetch_content_with_headers(url):
    """Fetch content from URL and return response with headers"""
    try:
        response = requests.head(url, allow_redirects=True, timeout=30)
        return {
            'status_code': response.status_code,
            'headers': response.headers,
            'success': True
        }
    except requests.RequestException as e:
        return {
            'status_code': 500,
            'headers': {},
            'success': False,
            'error': str(e)
        }

def fetch_content(url):
    """Fetch HTML content from URL"""
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        return {
            'content': response.text,
            'success': True
        }
    except requests.RequestException as e:
        return {
            'content': None,
            'success': False,
            'error': str(e)
        }

def extract_meta_data(html, default_host):
    """Extract title and thumbnail from HTML content"""
    pattern = r'<title>(.*?)</title>.*?<link.*?rel="apple-touch-icon".*?href="(.*?)"/?>'
    match = re.search(pattern, html, re.DOTALL)

    if match:
        title = match.group(1).strip()
        thumbnail_path = match.group(2).strip()
        thumbnail = f"https://{default_host}{thumbnail_path}"
        return {
            'title': title,
            'thumbnail': thumbnail
        }

    return {
        'title': 'Default Title',
        'thumbnail': Config.DEFAULT_THUMBNAIL
    }

def parse_url_full_custom(new_url, query_params):
    """Parse and rebuild URL with custom query parameters"""
    parsed_url = urlparse(new_url)

    # Parse existing query parameters
    existing_params = parse_qs(parsed_url.query)

    # Parse new query parameters
    new_params = {}
    if query_params:
        for param in query_params.split('&'):
            if '=' in param:
                key, value = param.split('=', 1)
                new_params[key] = value

    # Merge parameters, with new ones taking precedence
    updated_params = existing_params.copy()
    for key, value in new_params.items():
        updated_params[key] = [value]

    # Add default isDomain parameter if not present
    if 'isDomain' not in updated_params:
        updated_params['isDomain'] = ['true']

    # Rebuild query string
    query_string = urlencode(updated_params, doseq=True)

    # Reconstruct URL
    updated_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}?{query_string}"

    return updated_url

def load_fallback_html():
    """Load fallback HTML content"""
    try:
        with open(Config.FALLBACK_HTML_PATH, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return """
        <!DOCTYPE html>
        <html>
        <head><title>Service Unavailable</title></head>
        <body><h1>Service temporarily unavailable</h1></body>
        </html>
        """

def process_html_content(html_content, hostname, viding_host):
    """Process HTML content by replacing URLs"""
    # Replace hostname references
    html = html_content.replace(f'{hostname}/', f'{viding_host}/')

    # Fix Google Translate URLs
    html = html.replace('src="//translate.google.com', 'src="https://translate.google.com')

    # Fix relative URLs
    html = html.replace('src="/', f'src="https://{viding_host}/')
    html = html.replace('href="/', f'href="https://{viding_host}/')

    return html
