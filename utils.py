import re
import requests
import time
from urllib.parse import urlencode, parse_qs, urlparse
from flask import request
from config import Config
from logging_config import get_logger

logger = get_logger(__name__)

def get_hostname():
    """Extract hostname from request, removing www prefix if present"""
    host = request.headers.get('Host', '')
    if host.startswith('www.'):
        return host[4:]
    return host

def build_query_params():
    """Build query parameters from request args"""
    params = {}

    if request.args.get('name'):
        params['name'] = request.args.get('name')
    if request.args.get('event'):
        params['event'] = request.args.get('event')
    if request.args.get('rsvp') is not None:
        params['rsvp'] = request.args.get('rsvp')

    return urlencode(params)

def fetch_content_with_headers(url):
    """Fetch content from URL and return response with headers"""
    start_time = time.time()
    logger.info("Starting HEAD request", extra={'url': url})

    try:
        response = requests.head(url, allow_redirects=True, timeout=Config.REQUEST_TIMEOUT)
        duration = time.time() - start_time

        logger.info("HEAD request completed", extra={
            'url': url,
            'status_code': response.status_code,
            'duration_seconds': duration,
            'redirects': len(response.history)
        })

        if duration > Config.SLOW_REQUEST_THRESHOLD:
            logger.warning("Slow HEAD request detected", extra={
                'url': url,
                'duration_seconds': duration,
                'threshold': Config.SLOW_REQUEST_THRESHOLD
            })

        return {
            'status_code': response.status_code,
            'headers': response.headers,
            'success': True
        }
    except requests.RequestException as e:
        duration = time.time() - start_time
        logger.error("HEAD request failed", extra={
            'url': url,
            'error': str(e),
            'error_type': type(e).__name__,
            'duration_seconds': duration
        })
        return {
            'status_code': 500,
            'headers': {},
            'success': False,
            'error': str(e)
        }

def fetch_content(url):
    """Fetch HTML content from URL"""
    start_time = time.time()
    logger.info("Starting GET request", extra={'url': url})

    try:
        response = requests.get(url, timeout=Config.REQUEST_TIMEOUT)
        response.raise_for_status()
        duration = time.time() - start_time
        content_length = len(response.text)

        logger.info("GET request completed", extra={
            'url': url,
            'status_code': response.status_code,
            'duration_seconds': duration,
            'content_length': content_length,
            'content_type': response.headers.get('content-type', 'unknown')
        })

        if duration > Config.SLOW_REQUEST_THRESHOLD:
            logger.warning("Slow GET request detected", extra={
                'url': url,
                'duration_seconds': duration,
                'threshold': Config.SLOW_REQUEST_THRESHOLD
            })

        return {
            'content': response.text,
            'success': True
        }
    except requests.RequestException as e:
        duration = time.time() - start_time
        logger.error("GET request failed", extra={
            'url': url,
            'error': str(e),
            'error_type': type(e).__name__,
            'duration_seconds': duration
        })
        return {
            'content': None,
            'success': False,
            'error': str(e)
        }

def extract_meta_data(html, default_host):
    """Extract title and thumbnail from HTML content"""
    pattern = r'<title>(.*?)</title>.*?<link.*?rel="apple-touch-icon".*?href="(.*?)"/?>'
    match = re.search(pattern, html, re.DOTALL)

    if match:
        title = match.group(1).strip()
        thumbnail_path = match.group(2).strip()
        thumbnail = f"https://{default_host}{thumbnail_path}"
        return {
            'title': title,
            'thumbnail': thumbnail
        }

    return {
        'title': 'Default Title',
        'thumbnail': Config.DEFAULT_THUMBNAIL
    }

def parse_url_full_custom(new_url, query_params):
    """Parse and rebuild URL with custom query parameters"""
    parsed_url = urlparse(new_url)

    # Parse existing query parameters
    existing_params = parse_qs(parsed_url.query)

    # Parse new query parameters
    new_params = {}
    if query_params:
        for param in query_params.split('&'):
            if '=' in param:
                key, value = param.split('=', 1)
                new_params[key] = value

    # Merge parameters, with new ones taking precedence
    updated_params = existing_params.copy()
    for key, value in new_params.items():
        updated_params[key] = [value]

    # Add default isDomain parameter if not present
    if 'isDomain' not in updated_params:
        updated_params['isDomain'] = ['true']

    # Rebuild query string
    query_string = urlencode(updated_params, doseq=True)

    # Reconstruct URL
    updated_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}?{query_string}"

    return updated_url

def load_fallback_html():
    """Load fallback HTML content"""
    try:
        logger.info("Loading fallback HTML", extra={'file_path': Config.FALLBACK_HTML_PATH})
        with open(Config.FALLBACK_HTML_PATH, 'r', encoding='utf-8') as f:
            content = f.read()
            logger.info("Fallback HTML loaded successfully", extra={
                'file_path': Config.FALLBACK_HTML_PATH,
                'content_length': len(content)
            })
            return content
    except FileNotFoundError:
        logger.warning("Fallback HTML file not found, using default", extra={
            'file_path': Config.FALLBACK_HTML_PATH
        })
        return """
        <!DOCTYPE html>
        <html>
        <head><title>Service Unavailable</title></head>
        <body><h1>Service temporarily unavailable</h1></body>
        </html>
        """
    except Exception as e:
        logger.error("Error loading fallback HTML", extra={
            'file_path': Config.FALLBACK_HTML_PATH,
            'error': str(e),
            'error_type': type(e).__name__
        })
        return """
        <!DOCTYPE html>
        <html>
        <head><title>Service Unavailable</title></head>
        <body><h1>Service temporarily unavailable</h1></body>
        </html>
        """

def process_html_content(html_content, hostname, viding_host):
    """Process HTML content by replacing URLs"""
    # Replace hostname references
    html = html_content.replace(f'{hostname}/', f'{viding_host}/')

    # Fix Google Translate URLs
    html = html.replace('src="//translate.google.com', 'src="https://translate.google.com')

    # Fix relative URLs
    html = html.replace('src="/', f'src="https://{viding_host}/')
    html = html.replace('href="/', f'href="https://{viding_host}/')

    return html
