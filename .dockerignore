# Git
.git
.gitignore
README.md

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.pytest_cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
.AppleDouble
.LSOverride
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*~

# Project specific
php_backup_*/
*.tmp
*.temp
.local/
test_*.py
migrate.py

# PHP legacy files
*.php
composer.json
composer.lock
vendor/

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
docs/
*.md
