<?php
// Require Composer's autoloader to load .env variables
require __DIR__ . '/vendor/autoload.php';

// Load the environment variables from the .env file
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Get the vidingHost from the .env file
$vidingHost = $_ENV['HOST'];

if(substr($_SERVER['HTTP_HOST'], 0, 3) == "www") {
   $getHostName = substr($_SERVER['HTTP_HOST'], 4);
} else {
   $getHostName = $_SERVER['HTTP_HOST'];
}

// Dynamic formatURL using $vidingHost
$formatURL = "https://" . $vidingHost . "/tamuinvit/" . $getHostName . '/guestbook';
// $formatURL = "http://127.0.0.1:8001/tamuinvit/alexsita.viding.co/guestbook";
$loadHTML = @file_get_contents($formatURL);
if ($loadHTML === false) {
  // Fallback to local file if remote request fails
  $loadHTML = file_get_contents(__DIR__ . '/fallback.html');
  echo $loadHTML;
  die();
}

preg_match_all(
    '/<title>(.*?)<\/title>.*?<link.*?rel="apple-touch-icon".*?href="(.*?)"\/>/s',
    $loadHTML,
    $element, // will contain the blog posts
    PREG_SET_ORDER // formats data into an array of posts
);

$title = $element[0][1];
$thumbnail = "https://" . $vidingHost . $element[0][2];
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Required meta tags -->
  <meta charset="UTF-8">
  <?php echo $element[0][0]; ?>
  <style type="text/css">
  html { overflow: auto; }

  html, body, div, #fullFrame {
    margin: 0px;
    padding: 0px;
    height: 100%;
    border: none;
  }

  #fullFrame {
    display: block;
    width: 100%;
    border: none;
    overflow-y: auto;
    overflow-x: hidden;
  }
  </style>
</head>

<body>
  <iframe id="fullFrame" src="<?= $formatURL ?>"
    frameborder="0" marginheight="0" marginwidth="0" width="100%" height="100%" scrolling="auto" allowfullscreen>
  </iframe>
  <script>
  if(/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream){
    document.querySelector('meta[name=viewport]')
    .setAttribute(
      'content',
      'initial-scale=1.0001, minimum-scale=1.0001, maximum-scale=1.0001, user-scalable=no'
    );
  }
  </script>
</body>
</html>
