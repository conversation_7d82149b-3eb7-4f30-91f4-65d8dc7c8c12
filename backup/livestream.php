<?php
// Autoload and load environment variables
require __DIR__ . '/vendor/autoload.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Fetch host and format URL dynamically
$vidingHost = $_ENV['HOST'];
$getHostName = preg_replace('/^www\./', '', $_SERVER['HTTP_HOST']);
$formatURL = "https://{$vidingHost}/tamuinvit/{$getHostName}/livestream";
// $formatURL = "http://127.0.0.1:8001/tamuinvit/alexsita.viding.co/livestream";

// cURL to fetch the page content
function fetchContent($url) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        return ['error' => curl_error($ch)];
    }
    
    curl_close($ch);
    return ['content' => $response];
}

// Extract title and thumbnail from HTML
function extractMetaData($html, $defaultHost) {
    $pattern = '/<title>(.*?)<\/title>.*?<link.*?rel="apple-touch-icon".*?href="(.*?)"\/>/s';
    if (preg_match($pattern, $html, $matches)) {
        return [
            'title' => htmlspecialchars($matches[1], ENT_QUOTES, 'UTF-8'),
            'thumbnail' => "https://{$defaultHost}" . htmlspecialchars($matches[2], ENT_QUOTES, 'UTF-8')
        ];
    }
    return [
        'title' => 'Default Title',
        'thumbnail' => 'https://viding.co/images/icon/viding-logo-32x32.png',
    ];
}

// Fetch the page content
$response = fetchContent($formatURL);
if (isset($response['error'])) {
    // Handle cURL error gracefully
    $title = 'Error';
    $thumbnail = 'https://viding.co/images/icon/viding-logo-32x32.png';
    $loadHTML = file_get_contents(__DIR__ . '/fallback.html');
    echo $loadHTML;
    die();
} else {
    // Extract metadata and HTML content
    $loadHTML = $response['content'];
    $metaData = extractMetaData($loadHTML, $vidingHost);
    $title = $metaData['title'];
    $thumbnail = $metaData['thumbnail'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="UTF-8">
    <title><?= $title ?></title>
    <link rel="apple-touch-icon" href="<?= $thumbnail ?>" />
    <style type="text/css">
        html, body, div {
            margin: 0;
            padding: 0;
            border: none;
        }
    </style>
</head>
<body>
    <div id="content">
        <?= $loadHTML ?>
    </div>
    <script>
        // Adjust viewport for iOS devices
        if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
            document.querySelector('meta[name=viewport]')
                .setAttribute(
                    'content',
                    'initial-scale=1.0001, minimum-scale=1.0001, maximum-scale=1.0001, user-scalable=no'
                );
        }
    </script>
</body>
</html>
