<?php
// Require Composer's autoloader
require __DIR__ . '/vendor/autoload.php';

// Load the environment variables from the .env file
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// header("Access-Control-Allow-Origin: *");
// header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
// header("Access-Control-Allow-Headers: Content-Type, Authorization");
// header('Cache-Control: no-cache, no-store, must-revalidate');
// header('Pragma: no-cache');
// header('Expires: 0');

$vidingHost = $_ENV['HOST'];

if (substr($_SERVER['HTTP_HOST'], 0, 3) == "www") {
    $getHostName = substr($_SERVER['HTTP_HOST'], 4);
} else {
    $getHostName = $_SERVER['HTTP_HOST'];
}

if (isset($_GET['name'])) {
    $i  = "name=" . urlencode($_GET['name']);
} else {
    $i = '';
}

if (isset($_GET['event'])) {
    $j  = "event=" . urlencode($_GET['event']);
} else {
    $j = '';
}

if (isset($_GET['rsvp'])) {
    $k  = "rsvp=" . urlencode($_GET['rsvp']);
} else {
    $k = '';
}

// Combine parameters into one query string
$queryParams = http_build_query(array_filter([
    'name' => $_GET['name'] ?? null,
    'event' => $_GET['event'] ?? null,
    // Ensure rsvp=0 is not filtered out by array_filter
    'rsvp' => isset($_GET['rsvp']) ? $_GET['rsvp'] : null,
], function ($value) {
    // Keep the value if it's not null, including '0'
    return $value !== null;
}));

// Default URL for the host with /tamuinvit path
// $formatURL = "https://revamp.new-viding-web.test/tamuinvit/alexsita.viding.co?" . $queryParams;
// $formatURL = "https://viding.co/tamuinvit/11yearsofus.viding.co/?" . $queryParams;
// $formatURL = "https://viding.org/tamuinvit/custom-builder.viding.org?" . $queryParams;
// $formatURL = "http://127.0.0.1:8000/tamuinvit/alexsita.viding.co?" . $queryParams;
// $formatURL = "http://127.0.0.1:8001/tamuinvit/alexsita.viding.co?" . $queryParams;
$formatURL = "https://" . $vidingHost . "/tamuinvit/" . $getHostName . "?" . $queryParams;

// Initialize cURL session 
$ch = curl_init($formatURL);

// Set options to capture headers and response
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, true);

// Execute cURL session 
$response = curl_exec($ch);

// Get headers and body separately
$header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
$headers = substr($response, 0, $header_size);

// Parse headers
$header_lines = explode("\r\n", $headers);
$customHeaderDetected = false;
$newURL = '';
$parsedHeaders = [];
foreach ($header_lines as $header) {
    if (strpos($header, ': ') !== false) {
        list($key, $value) = explode(': ', $header, 2);
        $key = strtolower(trim($key)); // Convert header key to lowercase
        $parsedHeaders[$key] = trim($value);
    }
}

// Check custom headers (case insensitive)
if (isset($parsedHeaders['x-custom-builder']) && $parsedHeaders['x-custom-builder'] === '1') {
    $customHeaderDetected = true;
}
if (isset($parsedHeaders['x-builder-theme-url'])) {
    $newURL = trim($parsedHeaders['x-builder-theme-url']);
}

// Get the HTTP status code 
$retcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

// Test for 404 Error
if ($retcode != 200) {
    $url = "https://" . $vidingHost . "/comingsoon";
    $loadHTML = @file_get_contents($url);

    if ($loadHTML === false) {
        // Fallback to local file if remote request fails
        $loadHTML = file_get_contents(__DIR__ . '/fallback.html');
    }

    echo $loadHTML;
} else {
    // If the custom header is detected and new URL is provided, use it
    if ($customHeaderDetected && !empty($newURL)) {
        $link = parseUrlFullCustom($newURL, $queryParams);
        renderFullCustom($link, $parsedHeaders);
    }
    // Fetch content from the final URL
    $loadHTML = file_get_contents($formatURL);
    // Modify HTML, set cookies, etc., if the custom header is NOT detected
    $url = (isset($_SERVER['HTTPS']) ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
    setcookie("invitation_url", $url, 0, "/", $vidingHost);

    $html = str_replace($getHostName . '/', $vidingHost . '/', $loadHTML);
    $new_html = str_replace('src="//translate.google.com', 'src="https://translate.google.com', $html);
    $new_html = str_replace('src="/', 'src="https://' . $vidingHost . '/', $new_html);
    $new_html = str_replace('href="/', 'href="https://' . $vidingHost . '/', $new_html);

    echo $new_html;
}

// Close the cURL session
curl_close($ch);

function renderFullCustom($url, $parsedHeaders)
{
    $no_index = $parsedHeaders['x-no-index'] ?? 0;
    $custome_title = $parsedHeaders['x-custom-title'] ?? '';
    $event_date = $parsedHeaders['x-event-date'] ?? null;
    $wedding_city = $parsedHeaders['x-event-city'] ?? '';
    $bride_name = $parsedHeaders['x-bride-name'] ?? '';
    $groom_name = $parsedHeaders['x-groom-name'] ?? '';
    $switch_name = $parsedHeaders['x-change-name'] ?? 0;
    $wedding_url = $parsedHeaders['x-wedding-url'] ?? 'https://viding.co';
    $wedding_domain = $parsedHeaders['x-wedding-domain'] ?? 'viding.co';
    $thumbnail_url = $parsedHeaders['x-thumbnail-url'] ?? 'https://viding.co/images/icon/VIDING-WHITE-BG-PINK.jpg';


    if ($custome_title != '') {
        $meta_title = trim(nl2br($custome_title));
    } else {
        $meta_title =
            'Welcome to the Wedding of ' .
            ucfirst($switch_name ? $bride_name : $groom_name) .
            ' & ' .
            ucfirst($switch_name ? $groom_name : $bride_name) .
            ' ' .
            ucfirst($wedding_city ?? ' ') .
            ($event_date ? date('d F Y', strtotime($event_date)) : '');
    }

    $meta_description =
        ucfirst($switch_name ? $bride_name : $groom_name) .
        ' & ' .
        ucfirst($switch_name ? $groom_name : $bride_name) .
        ' Digital Invitation by Viding.co';

    echo '
<!DOCTYPE html>
<html lang="en" style="overscroll-behavior: none;>

<head>
    <!-- Required meta tags -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="keywords" content="Virtual Wedding, Digital Invitation, Wedding Invitation, Marvelous Wedding, Unforgettable Wedding, Viding.co">';

    // Add noindex if its true
    if ($no_index == 1) {
        echo '<meta name="robots" content="noindex">';
    }

    echo '
    <meta name="title" content="' . html_entity_decode($meta_title) . '">
    <meta name="description" content="' . html_entity_decode($meta_description) . '">

    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="' . htmlspecialchars($wedding_domain) . '" />
    <meta property="og:url" content="' . htmlspecialchars($wedding_url) . '" />
    <meta property="og:title" content="' . htmlspecialchars($meta_title) . '">
    <meta property="og:description" content="Digital Invitation by Viding.co">
    <meta property="og:image" content="' . htmlspecialchars($thumbnail_url) . '">
    <meta property="og:locale" content="id_ID">
    <meta property="og:image:type" content="image/jpeg">
    <meta property="og:image:width" content="650">
    <meta property="og:image:height" content="366">
    <meta property="og:image:alt" content="' . htmlspecialchars($meta_title) . '">

    <!-- Twitter Card Data -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@vidingco">
    <meta name="twitter:title" content="' . html_entity_decode($meta_title) . '">
    <meta name="twitter:description" content="Digital Invitation by Viding.co">
    <meta name="twitter:image:src" content="' . htmlspecialchars($thumbnail_url) . '">

    <title>' . html_entity_decode($meta_title) . '</title>
    
    <!-- Favicons -->
    <link rel="icon" href="' . htmlspecialchars($thumbnail_url) . '" sizes="192x192" />
    <link rel="icon" href="' . htmlspecialchars($thumbnail_url) . '" sizes="32x32" />
    <link rel="icon" href="' . htmlspecialchars($thumbnail_url) . '" sizes="16x16" />

    <!-- Google Translate -->
    <meta name="google" content="notranslate">
    <link rel="preconnect" href="https://media.viding.co">

    <!-- JSON-LD Schema Markup -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "headline": "' . html_entity_decode($meta_title) . '",
        "url": "' . htmlspecialchars($wedding_url) . '",
        "image": "' . htmlspecialchars($thumbnail_url) . '",
        "thumbnailUrl": "' . htmlspecialchars($thumbnail_url) . '"
    }
    </script>
    <style type="text/css">
        html {
            overflow: auto;
        }

        html,
        body,
        div,
        #fullFrame {
            margin: 0px;
            padding: 0px;
            height: 100%;
            border: none;
        }

        #fullFrame {
            display: block;
            width: 100%;
            border: none;
            overflow-y: auto;
            overflow-x: hidden;
        }
        body {
            overflow: hidden;
        }
    </style>
</head>

<body>
    <iframe id="fullFrame" src="' . htmlspecialchars($url) . '"
        frameborder="0" marginheight="0" marginwidth="0" width="100%" height="100%" scrolling="auto" allowfullscreen>
    </iframe>
    <script>
        if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
            document.querySelector(\'meta[name=viewport]\')
                .setAttribute(
                    \'content\',
                    \'initial-scale=1.0001, minimum-scale=1.0001, maximum-scale=1.0001, user-scalable=no\'
                );
        }
        // Enforce no zoom on other mobile devices
        document.addEventListener("gesturestart", function (e) {
            e.preventDefault(); // Prevent pinch zoom
        });

        document.addEventListener("dblclick", function (e) {
            e.preventDefault(); // Prevent double-tap zoom
        });
    </script>
</body>

</html>';
    exit();
}

function parseUrlFullCustom($newURL, $queryParams)
{
    // Parse the existing URL components
    $parsedUrl = parse_url($newURL);

    // Parse existing query parameters, if any
    $existingQueryParams = [];
    if (isset($parsedUrl['query'])) {
        parse_str($parsedUrl['query'], $existingQueryParams);
    }

    // Parse new query parameters
    parse_str($queryParams, $newQueryParams);

    // Merge the query parameters, replacing only 'name', 'event', 'rsvp'
    $updatedQueryParams = array_merge($existingQueryParams, $newQueryParams);

    // Add the default query param isDomain=true, if not already set
    if (!isset($updatedQueryParams['isDomain'])) {
        $updatedQueryParams['isDomain'] = 'true';
    }

    // Rebuild the query string
    $updatedQueryString = http_build_query($updatedQueryParams);

    // Reconstruct the full URL
    $updatedURL = "{$parsedUrl['scheme']}://{$parsedUrl['host']}{$parsedUrl['path']}?$updatedQueryString";

    return $updatedURL;
}
