import logging
import logging.handlers
import os
import sys
import uuid
from datetime import datetime
from flask import request, g
from pythonjsonlogger import jsonlogger


class RequestIDFilter(logging.Filter):
    """Add request ID to log records for traceability"""
    
    def filter(self, record):
        # Try to get request ID from Flask's g object
        try:
            record.request_id = getattr(g, 'request_id', 'no-request')
        except RuntimeError:
            # Outside of request context
            record.request_id = 'no-request'
        return True


class ContextualFormatter(logging.Formatter):
    """Custom formatter that adds contextual information"""
    
    def format(self, record):
        # Add timestamp
        record.timestamp = datetime.utcnow().isoformat()
        
        # Add request context if available
        try:
            if request:
                record.method = request.method
                record.url = request.url
                record.remote_addr = request.remote_addr
                record.user_agent = request.headers.get('User-Agent', 'Unknown')
        except RuntimeError:
            # Outside of request context
            pass
            
        return super().format(record)


def setup_logging(app):
    """Configure logging for the Flask application"""
    
    # Get configuration from app config
    log_level = app.config.get('LOG_LEVEL', 'INFO')
    log_file = app.config.get('LOG_FILE', 'logs/app.log')
    log_format = app.config.get('LOG_FORMAT', 'detailed')
    max_bytes = app.config.get('LOG_MAX_BYTES', 10 * 1024 * 1024)  # 10MB
    backup_count = app.config.get('LOG_BACKUP_COUNT', 5)
    
    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # Clear existing handlers
    app.logger.handlers.clear()
    
    # Set log level
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    app.logger.setLevel(numeric_level)
    
    # Create formatters
    if log_format == 'json':
        # JSON formatter for production
        json_formatter = jsonlogger.JsonFormatter(
            '%(timestamp)s %(name)s %(levelname)s %(request_id)s %(message)s'
        )
        console_formatter = json_formatter
        file_formatter = json_formatter
    else:
        # Detailed formatter for development
        detailed_format = (
            '%(asctime)s - %(name)s - %(levelname)s - '
            '[%(request_id)s] - %(funcName)s:%(lineno)d - %(message)s'
        )
        console_format = (
            '%(asctime)s - %(levelname)s - [%(request_id)s] - %(message)s'
        )
        
        console_formatter = ContextualFormatter(console_format)
        file_formatter = ContextualFormatter(detailed_format)
    
    # Add request ID filter
    request_filter = RequestIDFilter()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)
    console_handler.setFormatter(console_formatter)
    console_handler.addFilter(request_filter)
    app.logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(file_formatter)
        file_handler.addFilter(request_filter)
        app.logger.addHandler(file_handler)
    
    # Configure other loggers
    configure_external_loggers(numeric_level)
    
    # Add request logging middleware
    setup_request_logging(app)
    
    app.logger.info("Logging configured successfully", extra={
        'log_level': log_level,
        'log_file': log_file,
        'log_format': log_format
    })


def configure_external_loggers(level):
    """Configure logging for external libraries"""
    
    # Requests library
    requests_logger = logging.getLogger('requests')
    requests_logger.setLevel(logging.WARNING)
    
    # urllib3 (used by requests)
    urllib3_logger = logging.getLogger('urllib3')
    urllib3_logger.setLevel(logging.WARNING)
    
    # Werkzeug (Flask's WSGI server)
    werkzeug_logger = logging.getLogger('werkzeug')
    werkzeug_logger.setLevel(logging.WARNING)
    
    # Gunicorn
    gunicorn_logger = logging.getLogger('gunicorn')
    gunicorn_logger.setLevel(level)


def setup_request_logging(app):
    """Setup request/response logging middleware"""
    
    @app.before_request
    def before_request():
        # Generate unique request ID
        g.request_id = str(uuid.uuid4())[:8]
        g.start_time = datetime.utcnow()
        
        # Log incoming request
        app.logger.info("Request started", extra={
            'method': request.method,
            'url': request.url,
            'remote_addr': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', 'Unknown'),
            'content_length': request.content_length
        })
    
    @app.after_request
    def after_request(response):
        # Calculate request duration
        if hasattr(g, 'start_time'):
            duration = (datetime.utcnow() - g.start_time).total_seconds()
        else:
            duration = 0
        
        # Log response
        app.logger.info("Request completed", extra={
            'status_code': response.status_code,
            'content_length': response.content_length,
            'duration_seconds': duration
        })
        
        return response
    
    @app.errorhandler(Exception)
    def handle_exception(e):
        # Log unhandled exceptions
        app.logger.error("Unhandled exception occurred", extra={
            'exception_type': type(e).__name__,
            'exception_message': str(e)
        }, exc_info=True)
        
        # Return a generic error response
        return {
            'error': 'Internal server error',
            'request_id': getattr(g, 'request_id', 'unknown')
        }, 500


def get_logger(name):
    """Get a logger instance with proper configuration"""
    return logging.getLogger(name)
