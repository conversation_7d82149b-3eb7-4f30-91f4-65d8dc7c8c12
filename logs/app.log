2025-05-29 14:58:15,456 - test_simple_logging - INFO - [no-request] - setup_logging:116 - Logging configured successfully
2025-05-29 14:58:15,457 - test_simple_logging - INFO - [no-request] - <module>:55 - Flask app logging test - this should appear
2025-05-29 14:58:24,680 - app - INFO - [no-request] - setup_logging:116 - Logging configured successfully
2025-05-29 14:58:24,684 - app - INFO - [3d6698b3] - before_request:153 - Request started
2025-05-29 14:58:24,684 - app - DEBUG - [3d6698b3] - health:448 - Health check requested
2025-05-29 14:58:24,684 - app - INFO - [3d6698b3] - after_request:170 - Request completed
2025-05-29 14:58:34,109 - app - INFO - [no-request] - setup_logging:116 - Logging configured successfully
2025-05-29 14:58:47,371 - app - INFO - [no-request] - setup_logging:116 - Logging configured successfully
2025-05-29 14:58:47,615 - app - INFO - [no-request] - setup_logging:116 - Logging configured successfully
2025-05-29 14:58:59,948 - app - INFO - [44b3c523] - before_request:153 - Request started
2025-05-29 14:58:59,949 - app - INFO - [44b3c523] - after_request:170 - Request completed
2025-05-29 14:59:11,922 - app - INFO - [9e307300] - before_request:153 - Request started
2025-05-29 14:59:12,413 - app - INFO - [9e307300] - after_request:170 - Request completed
2025-05-29 14:59:52,601 - app - INFO - [no-request] - setup_logging:116 - Logging configured successfully
2025-05-29 14:59:52,605 - app - INFO - [ce57628a] - before_request:153 - Request started
2025-05-29 14:59:52,605 - app - DEBUG - [ce57628a] - health:448 - Health check requested
2025-05-29 14:59:52,606 - app - INFO - [ce57628a] - after_request:170 - Request completed
2025-05-29 14:59:52,606 - app - INFO - [48e20215] - before_request:153 - Request started
2025-05-29 14:59:52,606 - app - INFO - [48e20215] - index:178 - Processing index request
2025-05-29 14:59:52,606 - app - DEBUG - [48e20215] - index:189 - Built format URL
2025-05-29 14:59:52,787 - app - DEBUG - [48e20215] - index:216 - Checking custom headers
2025-05-29 14:59:52,970 - app - INFO - [48e20215] - index:244 - Successfully processed content
2025-05-29 14:59:52,972 - app - INFO - [48e20215] - after_request:170 - Request completed
2025-05-29 14:59:52,974 - app - INFO - [8912e1cb] - before_request:153 - Request started
2025-05-29 14:59:52,974 - app - INFO - [8912e1cb] - handle_simple_page:344 - Processing simple page request
2025-05-29 14:59:53,136 - app - ERROR - [8912e1cb] - handle_simple_page:353 - Failed to fetch simple page content
2025-05-29 14:59:53,137 - app - INFO - [8912e1cb] - after_request:170 - Request completed
2025-05-29 14:59:53,138 - app - INFO - [0042b47a] - before_request:153 - Request started
2025-05-29 14:59:53,138 - app - INFO - [0042b47a] - handle_simple_page:344 - Processing simple page request
2025-05-29 14:59:53,333 - app - ERROR - [0042b47a] - handle_simple_page:353 - Failed to fetch simple page content
2025-05-29 14:59:53,333 - app - INFO - [0042b47a] - after_request:170 - Request completed
2025-05-29 14:59:53,333 - app - INFO - [608e6960] - before_request:153 - Request started
2025-05-29 14:59:53,334 - app - INFO - [608e6960] - handle_simple_page:344 - Processing simple page request
2025-05-29 14:59:53,490 - app - ERROR - [608e6960] - handle_simple_page:353 - Failed to fetch simple page content
2025-05-29 14:59:53,491 - app - INFO - [608e6960] - after_request:170 - Request completed
