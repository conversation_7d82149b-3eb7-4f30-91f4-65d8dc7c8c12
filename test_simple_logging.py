#!/usr/bin/env python3
"""
Simple test to check if logging works
"""

import os
import sys

# Set environment variables for testing
os.environ['DEBUG'] = 'True'
os.environ['LOG_LEVEL'] = 'DEBUG'
os.environ['LOG_FORMAT'] = 'detailed'

print("Testing basic logging functionality...")

try:
    # Test basic logging first
    import logging
    
    # Configure basic logging to console
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    
    logger = logging.getLogger('test')
    logger.info("Basic logging test - this should appear in console")
    
    print("Basic logging works!")
    
    # Now test our custom logging
    print("Testing custom logging configuration...")
    
    from logging_config import get_logger
    
    custom_logger = get_logger('custom_test')
    custom_logger.info("Custom logger test - this should also appear")
    
    print("Custom logging works!")
    
    # Test Flask app logging
    print("Testing Flask app logging...")
    
    from flask import Flask
    from config import Config
    from logging_config import setup_logging
    
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Setup logging
    setup_logging(app)
    
    app.logger.info("Flask app logging test - this should appear")
    
    print("Flask app logging works!")
    
    print("\nAll logging tests passed!")
    
except Exception as e:
    print(f"Error during logging test: {e}")
    import traceback
    traceback.print_exc()
