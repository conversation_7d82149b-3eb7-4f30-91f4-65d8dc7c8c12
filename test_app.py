import unittest
from unittest.mock import patch, MagicMock
from app import app
import os

class TestVidingApp(unittest.TestCase):
    def setUp(self):
        """Set up test client"""
        self.app = app.test_client()
        self.app.testing = True
        
        # Set up environment variables for testing
        os.environ['HOST'] = 'alexsita.viding.co'
        os.environ['SECRET_KEY'] = 'test-secret-key'
        os.environ['DEBUG'] = 'True'

    def test_index_route_exists(self):
        """Test that the index route exists"""
        with patch('utils.fetch_content_with_headers') as mock_fetch_headers, \
             patch('utils.fetch_content') as mock_fetch_content:
            
            # Mock successful response
            mock_fetch_headers.return_value = {
                'status_code': 200,
                'headers': {},
                'success': True
            }
            
            mock_fetch_content.return_value = {
                'content': '<html><head><title>Test</title></head><body>Test Content</body></html>',
                'success': True
            }
            
            response = self.app.get('/')
            self.assertEqual(response.status_code, 200)

    def test_greeting_route_exists(self):
        """Test that the greeting route exists"""
        with patch('utils.fetch_content') as mock_fetch_content:
            mock_fetch_content.return_value = {
                'content': '<html><head><title>Greeting</title></head><body>Greeting Content</body></html>',
                'success': True
            }
            
            response = self.app.get('/greeting')
            self.assertEqual(response.status_code, 200)

    def test_rsvp_route_exists(self):
        """Test that the RSVP route exists"""
        with patch('utils.fetch_content') as mock_fetch_content:
            mock_fetch_content.return_value = {
                'content': '<html><head><title>RSVP</title></head><body>RSVP Content</body></html>',
                'success': True
            }
            
            response = self.app.get('/rsvp')
            self.assertEqual(response.status_code, 200)

    def test_guestbook_route_exists(self):
        """Test that the guestbook route exists"""
        with patch('utils.fetch_content') as mock_fetch_content:
            mock_fetch_content.return_value = {
                'content': '<html><head><title>Guestbook</title><link rel="apple-touch-icon" href="/icon.png"/></head><body>Guestbook Content</body></html>',
                'success': True
            }
            
            response = self.app.get('/guestbook')
            self.assertEqual(response.status_code, 200)

    def test_angpao_route_exists(self):
        """Test that the angpao route exists"""
        with patch('utils.fetch_content') as mock_fetch_content:
            mock_fetch_content.return_value = {
                'content': '<html><head><title>Angpao</title></head><body>Angpao Content</body></html>',
                'success': True
            }
            
            response = self.app.get('/angpao')
            self.assertEqual(response.status_code, 200)

    def test_livestream_route_exists(self):
        """Test that the livestream route exists"""
        with patch('utils.fetch_content') as mock_fetch_content:
            mock_fetch_content.return_value = {
                'content': '<html><head><title>Livestream</title></head><body>Livestream Content</body></html>',
                'success': True
            }
            
            response = self.app.get('/livestream')
            self.assertEqual(response.status_code, 200)

if __name__ == '__main__':
    unittest.main()
