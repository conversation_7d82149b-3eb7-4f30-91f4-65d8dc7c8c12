import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for the application"""

    # Get the HOST from environment variables
    VIDING_HOST = os.getenv('HOST', 'viding.co')

    # Flask configuration
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'

    # Default URLs and settings
    DEFAULT_THUMBNAIL = 'https://viding.co/images/icon/viding-logo-32x32.png'
    FALLBACK_HTML_PATH = 'fallback.html'

    # Logging configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'DEBUG' if DEBUG else 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'logs/app.log')
    LOG_FORMAT = os.getenv('LOG_FORMAT', 'json' if not DEBUG else 'detailed')
    LOG_MAX_BYTES = int(os.getenv('LOG_MAX_BYTES', 10 * 1024 * 1024))  # 10MB
    LOG_BACKUP_COUNT = int(os.getenv('LOG_BACKUP_COUNT', 5))

    # Request timeout configuration
    REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', 30))

    # Performance monitoring
    SLOW_REQUEST_THRESHOLD = float(os.getenv('SLOW_REQUEST_THRESHOLD', 2.0))  # seconds
