import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for the application"""
    
    # Get the HOST from environment variables
    VIDING_HOST = os.getenv('HOST', 'viding.co')
    
    # Flask configuration
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
    
    # Default URLs and settings
    DEFAULT_THUMBNAIL = 'https://viding.co/images/icon/viding-logo-32x32.png'
    FALLBACK_HTML_PATH = 'fallback.html'
